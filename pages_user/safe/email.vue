<!--
 * @Description: 绑定/修改邮箱页面
 * @Author: 徐静(parkhansung)
 * @Date: 2025-08-02
 * @Layout: 邮箱绑定表单
-->
<template>
  <view class="email-page">
    <!-- 顶部导航 -->
    <view class="nav-header">
      <view class="nav-back" @click="goBack">
        <text class="back-icon">‹</text>
      </view>
      <view class="nav-title">{{ pageTitle }}</view>
      <view class="nav-placeholder"></view>
    </view>

    <!-- 当前绑定信息 -->
    <view class="current-info" v-if="currentEmail">
      <view class="info-title">当前绑定邮箱</view>
      <view class="info-content">
        <text class="email-address">{{ maskedEmail }}</text>
        <text class="change-tip">修改绑定邮箱</text>
      </view>
    </view>

    <!-- 绑定表单 -->
    <view class="form-container">
      <view class="form-item">
        <view class="form-label">邮箱地址</view>
        <input 
          class="form-input" 
          type="text" 
          placeholder="请输入邮箱地址"
          v-model="formData.email"
        />
      </view>

      <view class="form-item">
        <view class="form-label">验证码</view>
        <view class="code-input-container">
          <input 
            class="form-input code-input" 
            type="number" 
            placeholder="请输入验证码"
            v-model="formData.code"
            maxlength="6"
          />
          <button 
            class="code-btn" 
            :class="{ 'disabled': !canSendCode || countdown > 0 }"
            @click="sendCode"
            :disabled="!canSendCode || countdown > 0"
          >
            {{ codeButtonText }}
          </button>
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="submit-container">
        <button 
          class="submit-btn" 
          :class="{ 'disabled': !canSubmit }"
          @click="handleSubmit"
          :disabled="!canSubmit"
        >
          {{ submitButtonText }}
        </button>
      </view>
    </view>

    <!-- 温馨提示 -->
    <view class="tips-container">
      <view class="tips-title">温馨提示</view>
      <view class="tips-content">
        <view class="tip-item">• 邮箱用于账户安全验证和找回密码</view>
        <view class="tip-item">• 请确保邮箱地址真实有效</view>
        <view class="tip-item">• 验证码有效期为10分钟</view>
        <view class="tip-item">• 如未收到邮件，请检查垃圾邮箱</view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  name: 'SafeEmail',
  data() {
    return {
      currentEmail: '', // 当前绑定的邮箱
      formData: {
        email: '',
        code: ''
      },
      countdown: 0, // 倒计时
      timer: null
    }
  },

  computed: {
    /**
     * 页面标题
     */
    pageTitle() {
      return this.currentEmail ? '修改邮箱' : '绑定邮箱'
    },

    /**
     * 脱敏邮箱
     */
    maskedEmail() {
      if (!this.currentEmail) return ''
      const [username, domain] = this.currentEmail.split('@')
      if (username.length > 3) {
        return `${username.substring(0, 3)}***@${domain}`
      }
      return `${username.substring(0, 1)}***@${domain}`
    },

    /**
     * 是否可以发送验证码
     */
    canSendCode() {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
      return emailRegex.test(this.formData.email)
    },

    /**
     * 验证码按钮文本
     */
    codeButtonText() {
      if (this.countdown > 0) {
        return `${this.countdown}s后重发`
      }
      return '发送验证码'
    },

    /**
     * 是否可以提交
     */
    canSubmit() {
      return this.canSendCode && /^\d{6}$/.test(this.formData.code)
    },

    /**
     * 提交按钮文本
     */
    submitButtonText() {
      return this.currentEmail ? '确认修改' : '确认绑定'
    }
  },

  onLoad() {
    this.loadCurrentEmail()
  },

  onUnload() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },

  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 加载当前邮箱
     */
    loadCurrentEmail() {
      try {
        const userInfo = uni.getStorageSync('userInfo')
        if (userInfo && userInfo.email) {
          this.currentEmail = userInfo.email
        }
      } catch (error) {
        console.error('加载用户信息失败:', error)
      }
    },

    /**
     * 发送验证码
     */
    async sendCode() {
      if (!this.canSendCode || this.countdown > 0) return

      try {
        uni.showLoading({
          title: '发送中...'
        })

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))

        uni.hideLoading()
        uni.showToast({
          title: '验证码已发送',
          icon: 'success'
        })

        // 开始倒计时
        this.startCountdown()

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '发送失败，请重试',
          icon: 'none'
        })
      }
    },

    /**
     * 开始倒计时
     */
    startCountdown() {
      this.countdown = 60
      this.timer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.timer)
          this.timer = null
        }
      }, 1000)
    },

    /**
     * 处理提交
     */
    handleSubmit() {
      if (!this.canSubmit) return

      // 这里应该调用API绑定/修改邮箱
      this.bindEmail()
    },

    /**
     * 绑定/修改邮箱
     */
    async bindEmail() {
      try {
        uni.showLoading({
          title: this.currentEmail ? '修改中...' : '绑定中...'
        })

        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1500))

        // 更新本地存储
        const userInfo = uni.getStorageSync('userInfo') || {}
        userInfo.email = this.formData.email
        uni.setStorageSync('userInfo', userInfo)

        uni.hideLoading()
        uni.showToast({
          title: this.currentEmail ? '修改成功' : '绑定成功',
          icon: 'success'
        })

        setTimeout(() => {
          uni.navigateBack()
        }, 1500)

      } catch (error) {
        uni.hideLoading()
        uni.showToast({
          title: '操作失败，请重试',
          icon: 'none'
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.email-page {
  min-height: 100vh;
  background: #f5f5f5;
}

/* 顶部导航 */
.nav-header {
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.nav-back {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.nav-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.nav-placeholder {
  width: 60rpx;
}

/* 当前信息 */
.current-info {
  background: #fff;
  margin: 20rpx 30rpx;
  padding: 30rpx;
  border-radius: 16rpx;
}

.info-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
}

.info-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.email-address {
  font-size: 32rpx;
  color: #333;
  font-weight: bold;
}

.change-tip {
  font-size: 26rpx;
  color: #007aff;
}

/* 表单容器 */
.form-container {
  padding: 0 30rpx;
}

.form-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: bold;
}

.form-input {
  width: 100%;
  font-size: 30rpx;
  color: #333;
}

.code-input-container {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.code-input {
  flex: 1;
}

.code-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  font-size: 26rpx;
  white-space: nowrap;
}

.code-btn.disabled {
  background: #e9ecef;
  color: #adb5bd;
}

/* 提交按钮 */
.submit-container {
  padding: 20rpx 0 40rpx;
}

.submit-btn {
  width: 100%;
  background: linear-gradient(135deg, #007aff 0%, #5ac8fa 100%);
  color: #fff;
  border: none;
  border-radius: 50rpx;
  padding: 30rpx;
  font-size: 32rpx;
  font-weight: bold;
}

.submit-btn.disabled {
  background: #e9ecef;
  color: #adb5bd;
}

/* 温馨提示 */
.tips-container {
  background: #fff;
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  padding: 30rpx;
}

.tips-title {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 20rpx;
  font-weight: bold;
}

.tips-content {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
}

.tip-item {
  font-size: 26rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 12rpx;
}

.tip-item:last-child {
  margin-bottom: 0;
}
</style>
